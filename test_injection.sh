#!/bin/bash

# Test script to verify the cheat is working
# This will show the debug output from the cheat

echo "Testing CS2 Internal Cheat..."

# Get CS2 process ID
CS2_PID=$(pgrep -f "cs2" | head -1)

if [ -z "$CS2_PID" ]; then
    echo "CS2 not running!"
    exit 1
fi

echo "CS2 PID: $CS2_PID"

# Check if our library is loaded
echo "Checking if library is loaded..."
if grep -q "libcs2_internal.so" /proc/$CS2_PID/maps; then
    echo "✅ Library is loaded!"
else
    echo "❌ Library not loaded"
    exit 1
fi

# Show memory maps containing our library
echo ""
echo "Library memory mapping:"
grep "libcs2_internal.so" /proc/$CS2_PID/maps

# Try to see if we can find any debug output
echo ""
echo "Checking for debug output (run this in another terminal):"
echo "journalctl -f | grep -i cs2"
echo ""
echo "Or check dmesg:"
echo "dmesg | tail -20"

# Show environment variables that might affect logging
echo ""
echo "Environment variables:"
echo "RUST_LOG=$RUST_LOG"

echo ""
echo "To see debug output, try:"
echo "1. Set RUST_LOG=debug before starting CS2"
echo "2. Check system logs: journalctl -f"
echo "3. Check dmesg output"
echo "4. The cheat should be rendering debug messages to the game's log"

# Test if we can send signals to the process
echo ""
echo "Testing process communication..."
if kill -0 $CS2_PID 2>/dev/null; then
    echo "✅ Can communicate with CS2 process"
else
    echo "❌ Cannot communicate with CS2 process"
fi

echo ""
echo "Cheat should be active! Look for debug messages in the game console or logs."
echo "Try pressing INSERT to toggle the menu (debug messages will show in logs)."
