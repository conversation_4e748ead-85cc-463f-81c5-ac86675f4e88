#!/bin/bash

# CS2 Internal Cheat Injection Script
# This script provides easy injection methods for the cheat

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get the library path
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LIB_PATH="$SCRIPT_DIR/target/release/libcs2_internal.so"

echo -e "${BLUE}CS2 Internal Cheat Injection Script${NC}"

# Check if library exists
if [ ! -f "$LIB_PATH" ]; then
    echo -e "${RED}Error: Library not found at $LIB_PATH${NC}"
    echo -e "${YELLOW}Please run ./build.sh first${NC}"
    exit 1
fi

# Function to inject into running CS2 process
inject_running() {
    echo -e "${YELLOW}Looking for running CS2 process...${NC}"

    # Try different possible process names
    CS2_PIDS=$(pgrep -f "cs2" 2>/dev/null || pgrep -f "Counter-Strike" 2>/dev/null || echo "")

    if [ -z "$CS2_PIDS" ]; then
        echo -e "${RED}CS2 process not found!${NC}"
        echo -e "${YELLOW}Make sure CS2 is running and try again.${NC}"
        echo -e "${YELLOW}Or use './inject.sh start' to start CS2 with the cheat preloaded.${NC}"
        exit 1
    fi

    echo -e "${GREEN}Found CS2 process(es) with PID(s): $CS2_PIDS${NC}"

    # Get the main CS2 process (usually the first one)
    MAIN_PID=$(echo $CS2_PIDS | awk '{print $1}')
    echo -e "${YELLOW}Using main process PID: $MAIN_PID${NC}"

    echo -e "${YELLOW}Injecting library using gdb...${NC}"

    # Create a temporary gdb script
    GDB_SCRIPT=$(mktemp)
    cat > "$GDB_SCRIPT" << EOF
set confirm off
call (void*)dlopen("$LIB_PATH", 2)
detach
quit
EOF

    # Inject using gdb with the script
    sudo gdb -p "$MAIN_PID" -x "$GDB_SCRIPT" -batch 2>/dev/null

    # Clean up
    rm -f "$GDB_SCRIPT"

    # Verify injection
    sleep 1
    if grep -q "libcs2_internal.so" /proc/$MAIN_PID/maps 2>/dev/null; then
        echo -e "${GREEN}Injection successful! Library is loaded.${NC}"
    else
        echo -e "${RED}Injection may have failed. Library not found in process memory.${NC}"
        echo -e "${YELLOW}Try using './inject.sh start' instead for more reliable injection.${NC}"
    fi
}

# Function to start CS2 with preload
start_with_preload() {
    echo -e "${YELLOW}Starting CS2 with LD_PRELOAD...${NC}"
    echo -e "${RED}Make sure to use -insecure and -allow_third_party_software launch options!${NC}"
    
    # Set environment variables for debugging
    export RUST_LOG=info
    export LD_PRELOAD="$LIB_PATH"
    
    # Start Steam with CS2
    steam steam://rungameid/730
}

# Function to show usage
show_usage() {
    echo -e "${BLUE}Usage: $0 [option]${NC}"
    echo ""
    echo -e "${YELLOW}Options:${NC}"
    echo -e "  ${GREEN}inject${NC}    - Inject into running CS2 process"
    echo -e "  ${GREEN}start${NC}     - Start CS2 with LD_PRELOAD"
    echo -e "  ${GREEN}help${NC}      - Show this help message"
    echo ""
    echo -e "${YELLOW}Examples:${NC}"
    echo -e "  ${GREEN}./inject.sh inject${NC}  # Inject into running CS2"
    echo -e "  ${GREEN}./inject.sh start${NC}   # Start CS2 with cheat"
    echo ""
    echo -e "${RED}IMPORTANT:${NC}"
    echo -e "${RED}- Only use on private servers with -insecure flag${NC}"
    echo -e "${RED}- This is for educational purposes only${NC}"
}

# Main script logic
case "${1:-help}" in
    "inject")
        inject_running
        ;;
    "start")
        start_with_preload
        ;;
    "help"|*)
        show_usage
        ;;
esac

echo ""
echo -e "${BLUE}=== CHEAT CONTROLS ===${NC}"
echo -e "${YELLOW}INSERT key: Toggle main menu${NC}"
echo -e "${YELLOW}Use the menu to configure features${NC}"
echo ""
echo -e "${GREEN}Happy learning! Remember to use responsibly.${NC}"
