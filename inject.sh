#!/bin/bash

# CS2 Internal Cheat Injection Script
# This script provides easy injection methods for the cheat

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get the library path
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LIB_PATH="$SCRIPT_DIR/target/release/libcs2_internal.so"

echo -e "${BLUE}CS2 Internal Cheat Injection Script${NC}"

# Check if library exists
if [ ! -f "$LIB_PATH" ]; then
    echo -e "${RED}Error: Library not found at $LIB_PATH${NC}"
    echo -e "${YELLOW}Please run ./build.sh first${NC}"
    exit 1
fi

# Function to inject into running CS2 process
inject_running() {
    echo -e "${YELLOW}Looking for running CS2 process...${NC}"

    # Find the actual CS2 game process (not Steam reaper or launcher)
    CS2_PID=$(pgrep -f "linuxsteamrt64/cs2" 2>/dev/null || pgrep -f "bin.*cs2" 2>/dev/null || echo "")

    if [ -z "$CS2_PID" ]; then
        echo -e "${RED}CS2 game process not found!${NC}"
        echo -e "${YELLOW}Looking for any CS2-related processes:${NC}"
        ps aux | grep -i cs2 | grep -v grep | head -5
        echo -e "${YELLOW}Make sure CS2 is actually running (not just Steam launcher).${NC}"
        echo -e "${YELLOW}Or use './inject.sh start' to start CS2 with the cheat preloaded.${NC}"
        exit 1
    fi

    # If multiple processes found, get the first one
    MAIN_PID=$(echo $CS2_PID | awk '{print $1}')

    echo -e "${GREEN}Found CS2 game process with PID: $MAIN_PID${NC}"

    # Verify it's the right process
    PROCESS_INFO=$(ps -p $MAIN_PID -o comm= 2>/dev/null || echo "unknown")
    echo -e "${BLUE}Process: $PROCESS_INFO${NC}"

    # Check if it's 64-bit
    ARCH_INFO=$(file /proc/$MAIN_PID/exe 2>/dev/null | grep -o "64-bit\|32-bit" || echo "unknown")
    echo -e "${BLUE}Architecture: $ARCH_INFO${NC}"

    if [[ "$ARCH_INFO" == "32-bit" ]]; then
        echo -e "${RED}Warning: CS2 process is 32-bit, but our library is 64-bit!${NC}"
        echo -e "${YELLOW}This injection will likely fail.${NC}"
    fi

    echo -e "${YELLOW}Injecting library using gdb...${NC}"

    # Create a temporary gdb script
    GDB_SCRIPT=$(mktemp)
    cat > "$GDB_SCRIPT" << EOF
set confirm off
set \$lib_handle = (void*)dlopen("$LIB_PATH", 2)
print \$lib_handle
call (void(*)())dlsym(\$lib_handle, "manual_init")
detach
quit
EOF

    # Show the gdb script for debugging
    echo -e "${BLUE}GDB Script contents:${NC}"
    cat "$GDB_SCRIPT"
    echo ""

    # Inject using gdb with the script (show output for debugging)
    echo -e "${YELLOW}Running gdb injection (this may take a moment)...${NC}"
    GDB_OUTPUT=$(sudo gdb -p "$MAIN_PID" -x "$GDB_SCRIPT" -batch 2>&1)
    GDB_EXIT_CODE=$?

    echo -e "${BLUE}GDB Output:${NC}"
    echo "$GDB_OUTPUT"
    echo -e "${BLUE}GDB Exit Code: $GDB_EXIT_CODE${NC}"

    # Clean up
    rm -f "$GDB_SCRIPT"

    # Verify injection
    echo -e "${YELLOW}Verifying injection...${NC}"
    sleep 2

    if grep -q "libcs2_internal.so" /proc/$MAIN_PID/maps 2>/dev/null; then
        echo -e "${GREEN}✅ Injection successful! Library is loaded in process memory.${NC}"
        echo -e "${YELLOW}Library mapping:${NC}"
        grep "libcs2_internal.so" /proc/$MAIN_PID/maps
    else
        echo -e "${RED}❌ Injection failed. Library not found in process memory.${NC}"
        echo -e "${YELLOW}Debugging info:${NC}"
        echo -e "${YELLOW}Process $MAIN_PID memory maps:${NC}"
        head -10 /proc/$MAIN_PID/maps
        echo "..."
        echo -e "${YELLOW}Try running with RUST_LOG=debug for more verbose output.${NC}"
    fi
}

# Alternative injection method using manual dlopen
try_manual_injection() {
    local PID=$1
    echo -e "${YELLOW}Trying manual injection method...${NC}"

    # Create a small C program to inject the library
    INJECT_C=$(mktemp --suffix=.c)
    INJECT_BIN=$(mktemp)

    cat > "$INJECT_C" << 'EOF'
#include <dlfcn.h>
#include <stdio.h>
#include <stdlib.h>

int main(int argc, char *argv[]) {
    if (argc != 2) {
        printf("Usage: %s <library_path>\n", argv[0]);
        return 1;
    }

    void *handle = dlopen(argv[1], RTLD_LAZY);
    if (!handle) {
        printf("Error: %s\n", dlerror());
        return 1;
    }

    printf("Library loaded successfully at %p\n", handle);

    // Keep the library loaded
    sleep(1);

    return 0;
}
EOF

    # Compile the injection program
    gcc -o "$INJECT_BIN" "$INJECT_C" -ldl 2>/dev/null

    if [ $? -eq 0 ]; then
        echo -e "${YELLOW}Running manual injection...${NC}"
        # This is a placeholder - manual injection is complex
        echo -e "${YELLOW}Manual injection would require more advanced techniques.${NC}"
    fi

    # Clean up
    rm -f "$INJECT_C" "$INJECT_BIN"
}

# Function to start CS2 with preload
start_with_preload() {
    echo -e "${YELLOW}Starting CS2 with LD_PRELOAD...${NC}"
    echo -e "${RED}Make sure to use -insecure and -allow_third_party_software launch options!${NC}"
    
    # Set environment variables for debugging
    export RUST_LOG=info
    export LD_PRELOAD="$LIB_PATH"
    
    # Start Steam with CS2
    steam steam://rungameid/730
}

# Function to show usage
show_usage() {
    echo -e "${BLUE}Usage: $0 [option]${NC}"
    echo ""
    echo -e "${YELLOW}Options:${NC}"
    echo -e "  ${GREEN}inject${NC}    - Inject into running CS2 process"
    echo -e "  ${GREEN}start${NC}     - Start CS2 with LD_PRELOAD"
    echo -e "  ${GREEN}help${NC}      - Show this help message"
    echo ""
    echo -e "${YELLOW}Examples:${NC}"
    echo -e "  ${GREEN}./inject.sh inject${NC}  # Inject into running CS2"
    echo -e "  ${GREEN}./inject.sh start${NC}   # Start CS2 with cheat"
    echo ""
    echo -e "${RED}IMPORTANT:${NC}"
    echo -e "${RED}- Only use on private servers with -insecure flag${NC}"
    echo -e "${RED}- This is for educational purposes only${NC}"
}

# Main script logic
case "${1:-help}" in
    "inject")
        inject_running
        ;;
    "start")
        start_with_preload
        ;;
    "help"|*)
        show_usage
        ;;
esac

echo ""
echo -e "${BLUE}=== CHEAT CONTROLS ===${NC}"
echo -e "${YELLOW}INSERT key: Toggle main menu${NC}"
echo -e "${YELLOW}Use the menu to configure features${NC}"
echo ""
echo -e "${GREEN}Happy learning! Remember to use responsibly.${NC}"
