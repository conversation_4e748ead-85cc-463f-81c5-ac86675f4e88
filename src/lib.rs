use std::ffi::c_void;
use std::sync::atomic::{AtomicBool, Ordering};
use std::sync::Arc;

use ash::vk;
use libc::{dlopen, dlsym, RTLD_LAZY, RTLD_NEXT};
use log::{debug, error, info, warn};
use once_cell::sync::Lazy;
use parking_lot::Mutex;

mod vulkan_hook;
mod imgui_overlay;
mod cheat_features;

use vulkan_hook::VulkanHook;

// Global state
static INITIALIZED: AtomicBool = AtomicBool::new(false);
static VULKAN_HOOK: Lazy<Arc<Mutex<Option<VulkanHook>>>> = Lazy::new(|| Arc::new(Mutex::new(None)));

// ImGui overlay state - using a simpler approach to avoid Send/Sync issues
static IMGUI_INITIALIZED: AtomicBool = AtomicBool::new(false);

// Original function pointers
type VkCreateSwapchainKHRFn = unsafe extern "system" fn(
    device: vk::Device,
    create_info: *const vk::SwapchainCreateInfoKHR,
    allocator: *const vk::AllocationCallbacks,
    swapchain: *mut vk::SwapchainKHR,
) -> vk::Result;

type VkQueuePresentKHRFn = unsafe extern "system" fn(
    queue: vk::Queue,
    present_info: *const vk::PresentInfoKHR,
) -> vk::Result;

static mut ORIGINAL_CREATE_SWAPCHAIN: Option<VkCreateSwapchainKHRFn> = None;
static mut ORIGINAL_QUEUE_PRESENT: Option<VkQueuePresentKHRFn> = None;

/// Initialize the cheat when the library is loaded
#[no_mangle]
pub extern "C" fn cheat_init() {
    if INITIALIZED.load(Ordering::Relaxed) {
        return;
    }

    // Initialize logging
    env_logger::Builder::from_default_env()
        .filter_level(log::LevelFilter::Debug)
        .init();

    info!("CS2 Internal Cheat initializing...");

    // Initialize Vulkan hooks
    if let Err(e) = initialize_vulkan_hooks() {
        error!("Failed to initialize Vulkan hooks: {}", e);
        return;
    }

    INITIALIZED.store(true, Ordering::Relaxed);
    info!("CS2 Internal Cheat initialized successfully!");
}

/// Initialize Vulkan function hooks
fn initialize_vulkan_hooks() -> Result<(), Box<dyn std::error::Error>> {
    unsafe {
        // Get original function pointers
        let lib = dlopen(std::ptr::null(), RTLD_LAZY);
        if lib.is_null() {
            return Err("Failed to open current process".into());
        }

        let create_swapchain_ptr = dlsym(lib, b"vkCreateSwapchainKHR\0".as_ptr() as *const i8);
        let queue_present_ptr = dlsym(lib, b"vkQueuePresentKHR\0".as_ptr() as *const i8);

        if create_swapchain_ptr.is_null() || queue_present_ptr.is_null() {
            // Try to get from RTLD_NEXT
            let create_swapchain_ptr = dlsym(RTLD_NEXT, b"vkCreateSwapchainKHR\0".as_ptr() as *const i8);
            let queue_present_ptr = dlsym(RTLD_NEXT, b"vkQueuePresentKHR\0".as_ptr() as *const i8);
            
            if create_swapchain_ptr.is_null() || queue_present_ptr.is_null() {
                return Err("Failed to find Vulkan functions".into());
            }
        }

        ORIGINAL_CREATE_SWAPCHAIN = Some(std::mem::transmute(create_swapchain_ptr));
        ORIGINAL_QUEUE_PRESENT = Some(std::mem::transmute(queue_present_ptr));
    }

    debug!("Vulkan hooks initialized");
    Ok(())
}

/// Hooked vkCreateSwapchainKHR function
#[no_mangle]
pub unsafe extern "system" fn vkCreateSwapchainKHR(
    device: vk::Device,
    create_info: *const vk::SwapchainCreateInfoKHR,
    allocator: *const vk::AllocationCallbacks,
    swapchain: *mut vk::SwapchainKHR,
) -> vk::Result {
    debug!("vkCreateSwapchainKHR called");

    // Call original function
    let result = if let Some(original) = ORIGINAL_CREATE_SWAPCHAIN {
        original(device, create_info, allocator, swapchain)
    } else {
        vk::Result::ERROR_INITIALIZATION_FAILED
    };

    if result == vk::Result::SUCCESS {
        // Initialize ImGui overlay when swapchain is created
        if let Err(e) = initialize_imgui_overlay(device, *swapchain, create_info) {
            error!("Failed to initialize ImGui overlay: {}", e);
        }
    }

    result
}

/// Hooked vkQueuePresentKHR function
#[no_mangle]
pub unsafe extern "system" fn vkQueuePresentKHR(
    queue: vk::Queue,
    present_info: *const vk::PresentInfoKHR,
) -> vk::Result {
    // For now, just log that we would render ImGui here
    // In a real implementation, you would render the ImGui overlay
    if IMGUI_INITIALIZED.load(Ordering::Relaxed) {
        debug!("Would render ImGui overlay here");
    }

    // Call original function
    if let Some(original) = ORIGINAL_QUEUE_PRESENT {
        original(queue, present_info)
    } else {
        vk::Result::ERROR_INITIALIZATION_FAILED
    }
}

/// Initialize ImGui overlay (simplified version)
unsafe fn initialize_imgui_overlay(
    _device: vk::Device,
    _swapchain: vk::SwapchainKHR,
    _create_info: *const vk::SwapchainCreateInfoKHR,
) -> Result<(), Box<dyn std::error::Error>> {
    // For now, just mark as initialized
    // In a real implementation, you would create the ImGui context here
    // but avoid the Send/Sync issues by keeping it thread-local
    IMGUI_INITIALIZED.store(true, Ordering::Relaxed);

    info!("ImGui overlay initialized (placeholder)");
    Ok(())
}

/// Cleanup function called when library is unloaded
#[no_mangle]
pub extern "C" fn cheat_cleanup() {
    info!("CS2 Internal Cheat shutting down...");

    // Cleanup ImGui overlay
    IMGUI_INITIALIZED.store(false, Ordering::Relaxed);

    // Cleanup Vulkan hook
    let mut hook_guard = VULKAN_HOOK.lock();
    *hook_guard = None;

    info!("CS2 Internal Cheat shutdown complete");
}
