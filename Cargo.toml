[package]
name = "cs2-internal"
version = "0.1.0"
edition = "2021"

[lib]
name = "cs2_internal"
crate-type = ["cdylib"]

[dependencies]
# ImGUI and Vulkan rendering
imgui = "0.12"
imgui-rs-vulkan-renderer = "1.16"
ash = "0.38"

# System and hooking
libc = "0.2"
libloading = "0.8"
once_cell = "1.19"
parking_lot = "0.12"
ctor = "0.2"

# Logging and error handling
log = "0.4"
env_logger = "0.11"
anyhow = "1.0"

# Memory management
gpu-allocator = { version = "0.27", features = ["vulkan"] }

# Math and utilities
nalgebra = "0.33"
bytemuck = "1.14"

[features]
default = []
debug = ["log/max_level_debug"]

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"

[profile.dev]
opt-level = 0
debug = true
