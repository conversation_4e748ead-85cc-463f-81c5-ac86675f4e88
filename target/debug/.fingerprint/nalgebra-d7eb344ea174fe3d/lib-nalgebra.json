{"rustc": 11410426090777951712, "features": "[\"default\", \"macros\", \"matrixmultiply\", \"nalgebra-macros\", \"std\"]", "declared_features": "[\"alga\", \"alloc\", \"arbitrary\", \"bytemuck\", \"compare\", \"convert-bytemuck\", \"convert-glam014\", \"convert-glam015\", \"convert-glam016\", \"convert-glam017\", \"convert-glam018\", \"convert-glam019\", \"convert-glam020\", \"convert-glam021\", \"convert-glam022\", \"convert-glam023\", \"convert-glam024\", \"convert-glam025\", \"convert-glam027\", \"convert-glam028\", \"convert-glam029\", \"convert-mint\", \"debug\", \"default\", \"glam014\", \"glam015\", \"glam016\", \"glam017\", \"glam018\", \"glam019\", \"glam020\", \"glam021\", \"glam022\", \"glam023\", \"glam024\", \"glam025\", \"glam027\", \"glam028\", \"glam029\", \"io\", \"libm\", \"libm-force\", \"macros\", \"matrixcompare-core\", \"matrixmultiply\", \"mint\", \"nalgebra-macros\", \"pest\", \"pest_derive\", \"proptest\", \"proptest-support\", \"quickcheck\", \"rand\", \"rand-no-std\", \"rand-package\", \"rand_distr\", \"rayon\", \"rkyv\", \"rkyv-safe-deser\", \"rkyv-serialize\", \"rkyv-serialize-no-std\", \"serde\", \"serde-serialize\", \"serde-serialize-no-std\", \"slow-tests\", \"sparse\", \"std\"]", "target": 572955357253318494, "profile": 2241668132362809309, "path": 1773654236887130937, "deps": [[2819946551904607991, "num_rational", false, 16876861053608827246], [5157631553186200874, "num_traits", false, 10315985853949066561], [6079319712623925900, "simba", false, 1907113328322264746], [11394677342629719743, "nalgebra_macros", false, 16569321717463253502], [12319020793864570031, "num_complex", false, 74903353225010121], [15677050387741058262, "approx", false, 6114195229962682263], [15826188163127377936, "matrixmultiply", false, 7792486050020894351], [17001665395952474378, "typenum", false, 701711200953991908]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/nalgebra-d7eb344ea174fe3d/dep-lib-nalgebra", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}