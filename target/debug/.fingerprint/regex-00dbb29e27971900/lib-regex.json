{"rustc": 11410426090777951712, "features": "[\"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 2241668132362809309, "path": 4644373299241085832, "deps": [[555019317135488525, "regex_automata", false, 5949427747652917299], [2779309023524819297, "aho_corasick", false, 10830329105619863772], [9408802513701742484, "regex_syntax", false, 6658698943667179372], [15932120279885307830, "memchr", false, 11793420484143003407]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-00dbb29e27971900/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}