cargo:THIRD_PARTY=/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/imgui-sys-0.12.0/third-party
cargo:DEFINE_IMGUI_USE_WCHAR32=
cargo:DEFINE_CIMGUI_NO_EXPORT=
cargo:DEFINE_IMGUI_DISABLE_WIN32_FUNCTIONS=
cargo:DEFINE_IMGUI_DISABLE_OSX_FUNCTIONS=
cargo:THIRD_PARTY=/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/imgui-sys-0.12.0/third-party/imgui-master
OPT_LEVEL = Some(0)
OUT_DIR = Some(/home/<USER>/Desktop/cs2-internal/target/debug/build/imgui-sys-75c3a9bc16399d73/out)
TARGET = Some(x86_64-unknown-linux-gnu)
HOST = Some(x86_64-unknown-linux-gnu)
cargo:rerun-if-env-changed=CXX_x86_64-unknown-linux-gnu
CXX_x86_64-unknown-linux-gnu = None
cargo:rerun-if-env-changed=CXX_x86_64_unknown_linux_gnu
CXX_x86_64_unknown_linux_gnu = None
cargo:rerun-if-env-changed=HOST_CXX
HOST_CXX = None
cargo:rerun-if-env-changed=CXX
CXX = None
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = Some(/home/<USER>/.vscode/extensions/rust-lang.rust-analyzer-0.3.2547-linux-x64/server/rust-analyzer)
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
CARGO_CFG_TARGET_FEATURE = Some(fxsr,sse,sse2)
cargo:rerun-if-env-changed=CXXFLAGS
CXXFLAGS = None
cargo:rerun-if-env-changed=HOST_CXXFLAGS
HOST_CXXFLAGS = None
cargo:rerun-if-env-changed=CXXFLAGS_x86_64_unknown_linux_gnu
CXXFLAGS_x86_64_unknown_linux_gnu = None
cargo:rerun-if-env-changed=CXXFLAGS_x86_64-unknown-linux-gnu
CXXFLAGS_x86_64-unknown-linux-gnu = None
CARGO_ENCODED_RUSTFLAGS = Some()
OUT_DIR = Some(/home/<USER>/Desktop/cs2-internal/target/debug/build/imgui-sys-75c3a9bc16399d73/out)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
TARGET = Some(x86_64-unknown-linux-gnu)
CARGO_CFG_TARGET_FEATURE = Some(fxsr,sse,sse2)
HOST = Some(x86_64-unknown-linux-gnu)
cargo:rerun-if-env-changed=CXXFLAGS
CXXFLAGS = None
cargo:rerun-if-env-changed=HOST_CXXFLAGS
HOST_CXXFLAGS = None
cargo:rerun-if-env-changed=CXXFLAGS_x86_64_unknown_linux_gnu
CXXFLAGS_x86_64_unknown_linux_gnu = None
cargo:rerun-if-env-changed=CXXFLAGS_x86_64-unknown-linux-gnu
CXXFLAGS_x86_64-unknown-linux-gnu = None
cargo:rerun-if-env-changed=AR_x86_64-unknown-linux-gnu
AR_x86_64-unknown-linux-gnu = None
cargo:rerun-if-env-changed=AR_x86_64_unknown_linux_gnu
AR_x86_64_unknown_linux_gnu = None
cargo:rerun-if-env-changed=HOST_AR
HOST_AR = None
cargo:rerun-if-env-changed=AR
AR = None
cargo:rerun-if-env-changed=ARFLAGS
ARFLAGS = None
cargo:rerun-if-env-changed=HOST_ARFLAGS
HOST_ARFLAGS = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64_unknown_linux_gnu
ARFLAGS_x86_64_unknown_linux_gnu = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64-unknown-linux-gnu
ARFLAGS_x86_64-unknown-linux-gnu = None
cargo:rustc-link-lib=static=cimgui
cargo:rustc-link-search=native=/home/<USER>/Desktop/cs2-internal/target/debug/build/imgui-sys-75c3a9bc16399d73/out
cargo:rerun-if-env-changed=CXXSTDLIB_x86_64-unknown-linux-gnu
CXXSTDLIB_x86_64-unknown-linux-gnu = None
cargo:rerun-if-env-changed=CXXSTDLIB_x86_64_unknown_linux_gnu
CXXSTDLIB_x86_64_unknown_linux_gnu = None
cargo:rerun-if-env-changed=HOST_CXXSTDLIB
HOST_CXXSTDLIB = None
cargo:rerun-if-env-changed=CXXSTDLIB
CXXSTDLIB = None
cargo:rustc-link-lib=stdc++
